{"name": "tanstack", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "start": "vite --port 3000", "build": "vite build && tsc", "serve": "vite preview", "test": "vitest run"}, "dependencies": {"@tailwindcss/vite": "^4.1.8", "@tanstack/react-router": "^1.114.3", "@tanstack/react-router-devtools": "^1.114.3", "@tanstack/router-plugin": "^1.114.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "^4.1.8"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "jsdom": "^26.0.0", "typescript": "^5.7.2", "vite": "^6.1.0", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}}